[project]
name = "life-agent-demo"
version = "0.1.0"
description = "A LangGraph-based AI assistant for life management"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "click>=8.2.1",
    "langchain-core>=0.3.69",
    "langchain-openai>=0.3.28",
    "langgraph>=0.5.3",
    "python-dotenv>=1.1.1",
]

[project.scripts]
life-agent = "src.life_agent.cli.main:cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/life_agent"]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true
