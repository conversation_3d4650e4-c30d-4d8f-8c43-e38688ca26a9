# -*- coding: utf-8 -*-
"""Human Tool Interface - The Agent's primary tool for interacting with humans."""

from typing import Dict, Any, List, Optional, Tuple
from langchain_core.tools import tool
from ..core.memory import ExecutionStatus


class HumanToolInterface:
    """
    The Human Tool Interface - Agent's way to interact with users.
    This treats the human as a tool that the Agent can call to get information and trigger actions.
    """
    
    def __init__(self, cli_interface=None):
        """Initialize with optional CLI interface for testing."""
        self.cli_interface = cli_interface
        self.pending_responses = {}
    
    def to_tool_list(self):
        """Get tool versions of the methods for LangGraph."""
        return [
            get_goal_details_tool,
            get_sub_tasks_tool,
            trigger_execution_tool,
            get_execution_result_tool,
        ]

    def get_goal_details(self) -> Tuple[str, str]:
        """
        Request goal details from the human.
        Returns: (goal_name, goal_description)
        """
        goal_description = self.cli_interface.prompt("Please describe this goal in detail:")
        return '', goal_description
    
    def get_sub_tasks(self, goal_name: str) -> List[Dict[str, str]]:
        """
        Tool for Agent to request task breakdown from the human.
        Returns: List of tasks with name and description
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\nLet's break down your goal '{goal_name}' into actionable tasks.")
            tasks = []
            while True:
                task_name = self.cli_interface.prompt("Enter a task name (or 'done' to finish):")
                if task_name.lower() == 'done':
                    break
                task_desc = self.cli_interface.prompt(f"Describe the task '{task_name}':")
                tasks.append({"name": task_name, "description": task_desc})
            return tasks
        else:
            # Mock data for testing
            return [
                {"name": "Sample Task 1", "description": "First sample task"},
                {"name": "Sample Task 2", "description": "Second sample task"}
            ]
    
    def get_task_trigger(self, task_name: str) -> Dict[str, str]:
        """
        Tool for Agent to request task scheduling from the human.
        Returns: {"frequency": "daily/weekly/etc", "time": "HH:MM", "duration": "minutes"}
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\nWhen should I remind you to do '{task_name}'?")
            frequency = self.cli_interface.prompt("Frequency (daily/weekly/monthly):")
            time = self.cli_interface.prompt("Time (HH:MM format, e.g., 09:00):")
            duration = self.cli_interface.prompt("Estimated duration in minutes:")
            return {
                "frequency": frequency,
                "time": time,
                "duration": duration
            }
        else:
            return {"frequency": "daily", "time": "09:00", "duration": "30"}
    
    def present_insight(self, insight: str) -> str:
        """
        Tool for Agent to present insights and get user feedback.
        Returns: User's response to the insight
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\n[INSIGHT] {insight}")
            response = self.cli_interface.prompt("What do you think? (agree/disagree/modify):")
            return response
        else:
            return "agree"
    
    def trigger_execution(self, task_info: Dict[str, Any]) -> str:
        """
        Tool for Agent to trigger task execution.
        This would normally send a push notification to the user's device.
        Returns: Initial user response ('STARTED', 'DEFERRED', 'IGNORED')
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\n[REMINDER] Time to do: {task_info['name']}")
            self.cli_interface.echo(f"Description: {task_info['description']}")
            response = self.cli_interface.prompt("Will you start this task now? (yes/later/no):")
            
            if response.lower() in ['yes', 'y']:
                return 'STARTED'
            elif response.lower() in ['later', 'l']:
                return 'DEFERRED'
            else:
                return 'IGNORED'
        else:
            # Mock response for testing
            return 'STARTED'
    
    def get_execution_result(self, task_info: Dict[str, Any]) -> Tuple[str, Optional[str]]:
        """
        Tool for Agent to check task completion status.
        Returns: (status, feedback) where status is 'COMPLETED' or 'FAILED'
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\nHow did the task '{task_info['name']}' go?")
            status = self.cli_interface.prompt("Status (completed/failed):")
            feedback = self.cli_interface.prompt("Any feedback or notes:")
            
            return ('COMPLETED' if status.lower() == 'completed' else 'FAILED', feedback)
        else:
            return ('COMPLETED', 'Task completed successfully')
    
    def get_failure_reason(self, task_info: Dict[str, Any]) -> str:
        """
        Tool for Agent to understand why a task failed.
        Returns: Failure reason tag
        """
        if self.cli_interface:
            self.cli_interface.echo(f"\nWhy didn't you complete '{task_info['name']}'?")
            self.cli_interface.echo("Common reasons:")
            self.cli_interface.echo("1. No time")
            self.cli_interface.echo("2. Forgot")
            self.cli_interface.echo("3. Not motivated")
            self.cli_interface.echo("4. Too difficult")
            self.cli_interface.echo("5. Other")
            
            choice = self.cli_interface.prompt("Choose a reason (1-5) or describe:")
            
            reason_map = {
                "1": "no_time",
                "2": "forgot",
                "3": "not_motivated", 
                "4": "too_difficult",
                "5": "other"
            }
            
            return reason_map.get(choice, choice)
        else:
            return "no_time"
    
    def present_reflection_report(self, report: Dict[str, Any]) -> str:
        """
        Tool for Agent to present weekly reflection and get feedback.
        Returns: User's response to the report
        """
        if self.cli_interface:
            self.cli_interface.echo("\n=== WEEKLY REFLECTION REPORT ===")
            self.cli_interface.echo(f"Success Rate: {report.get('success_rate', 0)}%")
            self.cli_interface.echo(f"Most Common Failure: {report.get('common_failure', 'N/A')}")
            self.cli_interface.echo(f"Best Time Slot: {report.get('best_time', 'N/A')}")
            self.cli_interface.echo(f"Recommendations: {report.get('recommendations', 'None')}")
            
            response = self.cli_interface.prompt("Would you like to adjust your schedule based on this? (yes/no):")
            return response
        else:
            return "yes"

    def present_plan_for_confirmation(self, plan_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Present the Agent's generated plan to the human for confirmation.
        Returns: Confirmation response with any requested adjustments
        """
        if self.cli_interface:
            self.cli_interface.echo("\n=== AGENT'S INTELLIGENT PLAN ===")
            self.cli_interface.echo(f"Goal: {plan_result.get('goal_summary', 'N/A')}")
            self.cli_interface.echo(f"Strategy: {plan_result.get('scheduling_strategy', 'N/A')}")
            self.cli_interface.echo(f"Timeline: {plan_result.get('estimated_timeline', 'N/A')}")

            tasks = plan_result.get('tasks', [])
            self.cli_interface.echo(f"\nProposed Tasks ({len(tasks)}):")
            for i, task in enumerate(tasks, 1):
                self.cli_interface.echo(f"  {i}. {task.get('name', 'Task')}")
                self.cli_interface.echo(f"     {task.get('frequency', 'daily')} at {task.get('time', '09:00')} ({task.get('duration', 30)}min)")
                self.cli_interface.echo(f"     {task.get('description', 'No description')}")

            self.cli_interface.echo(f"\nOptimization: {plan_result.get('success_optimization', 'N/A')}")

            response = self.cli_interface.prompt("\nDo you approve this plan? (yes/no/adjust):")

            if response.lower() in ['yes', 'y']:
                return {"approved": True, "needs_adjustment": False}
            elif response.lower() in ['no', 'n']:
                return {"approved": False, "needs_adjustment": False, "reason": "User rejected plan"}
            else:  # adjust
                feedback = self.cli_interface.prompt("What would you like to adjust?:")
                return {
                    "approved": False,
                    "needs_adjustment": True,
                    "adjustment_feedback": feedback
                }
        else:
            # Mock approval for testing
            return {"approved": True, "needs_adjustment": False}


# Tool functions for LangGraph integration
@tool
def get_goal_details_tool() -> str:
    """Get goal details from the human user."""
    interface = HumanToolInterface()
    name, desc = interface.get_goal_details()
    return f"Goal: {name}\nDescription: {desc}"


@tool
def get_sub_tasks_tool(goal_name: str) -> str:
    """Get task breakdown from the human user."""
    interface = HumanToolInterface()
    tasks = interface.get_sub_tasks(goal_name)
    return f"Tasks: {tasks}"


@tool
def trigger_execution_tool(task_name: str, task_description: str) -> str:
    """Trigger task execution reminder to the human user."""
    interface = HumanToolInterface()
    task_info = {"name": task_name, "description": task_description}
    response = interface.trigger_execution(task_info)
    return f"User response: {response}"


@tool
def get_execution_result_tool(task_name: str) -> str:
    """Check task completion status with the human user."""
    interface = HumanToolInterface()
    task_info = {"name": task_name}
    status, feedback = interface.get_execution_result(task_info)
    return f"Status: {status}, Feedback: {feedback}"


def get_human_tools() -> List:
    """Get the list of human interaction tools."""
    return [
        get_goal_details_tool,
        get_sub_tasks_tool,
        trigger_execution_tool,
        get_execution_result_tool,
    ]
