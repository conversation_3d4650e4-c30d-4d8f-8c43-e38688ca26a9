"""Basic tools for the Life Agent."""

import datetime
import math
from typing import List
from langchain_core.tools import tool


@tool
def get_current_time() -> str:
    """Get the current date and time."""
    now = datetime.datetime.now()
    return f"Current date and time: {now.strftime('%Y-%m-%d %H:%M:%S')}"


@tool
def calculate(expression: str) -> str:
    """
    Perform basic mathematical calculations.
    
    Args:
        expression: A mathematical expression to evaluate (e.g., "2 + 3 * 4")
    
    Returns:
        The result of the calculation as a string.
    """
    try:
        # For safety, only allow basic mathematical operations
        allowed_chars = set('0123456789+-*/()., ')
        if not all(c in allowed_chars for c in expression):
            return "Error: Invalid characters in expression. Only numbers and basic operators (+, -, *, /, (), .) are allowed."
        
        # Evaluate the expression
        result = eval(expression)
        return f"Result: {result}"
    except Exception as e:
        return f"Error calculating '{expression}': {str(e)}"


@tool
def get_day_of_week(date_str: str) -> str:
    """
    Get the day of the week for a given date.
    
    Args:
        date_str: Date in YYYY-MM-DD format
    
    Returns:
        The day of the week for the given date.
    """
    try:
        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
        day_name = date_obj.strftime('%A')
        return f"The date {date_str} falls on a {day_name}."
    except ValueError:
        return "Error: Please provide the date in YYYY-MM-DD format (e.g., 2024-01-15)."


@tool
def days_between_dates(start_date: str, end_date: str) -> str:
    """
    Calculate the number of days between two dates.
    
    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
    
    Returns:
        The number of days between the two dates.
    """
    try:
        start = datetime.datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.datetime.strptime(end_date, '%Y-%m-%d')
        difference = end - start
        days = difference.days
        
        if days == 0:
            return f"The dates {start_date} and {end_date} are the same day."
        elif days > 0:
            return f"There are {days} days from {start_date} to {end_date}."
        else:
            return f"There are {abs(days)} days from {end_date} to {start_date} (end date is before start date)."
    except ValueError:
        return "Error: Please provide dates in YYYY-MM-DD format (e.g., 2024-01-15)."


def get_basic_tools() -> List:
    """Get the list of basic tools available to the agent."""
    return [
        get_current_time,
        calculate,
        get_day_of_week,
        days_between_dates,
    ]
