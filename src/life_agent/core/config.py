"""Configuration management for the Life Agent."""

import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv(f'.env.{os.getenv("ENV", "dev")}')


class AgentConfig(BaseModel):
    """Configuration for the Life Agent."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY", ""))
    openai_base_url: Optional[str] = Field(default_factory=lambda: os.getenv("OPENAI_BASE_URL"))
    model: str = Field(default_factory=lambda: os.getenv("DEFAULT_MODEL", "gpt-4o-mini"))
    temperature: float = Field(default_factory=lambda: float(os.getenv("TEMPERATURE", "0.7")))
    max_tokens: int = Field(default_factory=lambda: int(os.getenv("MAX_TOKENS", "1000")))
    
    # LangSmith Configuration
    langchain_tracing_v2: bool = Field(default_factory=lambda: os.getenv("LANGCHAIN_TRACING_V2", "false").lower() == "true")
    langchain_endpoint: Optional[str] = Field(default_factory=lambda: os.getenv("LANGCHAIN_ENDPOINT"))
    langchain_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("LANGCHAIN_API_KEY"))
    langchain_project: str = Field(default_factory=lambda: os.getenv("LANGCHAIN_PROJECT", "life-agent-demo"))
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present."""
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY is required")
        return True


def get_config() -> AgentConfig:
    """Get the agent configuration."""
    config = AgentConfig()
    config.validate_config()
    return config
