"""Main Life Agent implementation using LangGraph - A goal-driven AI that treats humans as tools."""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
from enum import Enum

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode

from .config import get_config
from .state import AgentState
from .memory import SystemMemory
from ..tools.human_tool import HumanToolInterface
from ..tools.basic_tools import get_basic_tools


class LifeAgent:
    """
    Main Life Agent class - A goal-driven AI that treats humans as tools.

    This agent operates on a Plan-Act-React-Reflect cycle:
    - PLAN: Create goals and break them into actionable tasks
    - ACT: Trigger task execution at scheduled times
    - REACT: Process user responses and update execution status
    - REFLECT: Analyze patterns and generate insights for improvement
    """

    def __init__(self, cli_interface=None):
        """Initialize the Life Agent."""
        self.config = get_config()
        self.memory = SystemMemory()
        self.human_tool = HumanToolInterface(cli_interface)

        # LLM for reasoning and analysis
        self.llm = ChatOpenAI(
            api_key=self.config.openai_api_key,
            base_url=self.config.openai_base_url,
            model=self.config.model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )

        # Get available tools (including human tools)
        self.tools = get_basic_tools() + self.human_tool.to_tool_list()
        self.llm_with_tools = self.llm.bind_tools(self.tools)

        # Create the main loop graph
        self.graph = self._create_graph()

        # Agent state
        self.is_running = False
        self.current_state = None
    
    def _create_graph(self) -> StateGraph:
        """Create the Plan-Act-React-Reflect workflow graph."""
        from .state import AgentPhase

        workflow = StateGraph(AgentState)

        # Add nodes for each phase
        workflow.add_node("plan", self._plan_phase)
        workflow.add_node("act", self._act_phase)
        workflow.add_node("react", self._react_phase)
        workflow.add_node("reflect", self._reflect_phase)
        workflow.add_node("idle", self._idle_phase)

        # Set entry point
        workflow.set_entry_point("idle")

        # Add conditional edges based on current phase
        workflow.add_conditional_edges(
            "idle",
            self._route_from_idle,
            {
                "plan": "plan",
                "act": "act",
                "reflect": "reflect",
                "idle": "idle",
                "end": END
            }
        )

        workflow.add_conditional_edges(
            "plan",
            self._route_from_plan,
            {
                "idle": "idle",
                "act": "act",
                "end": END
            }
        )

        workflow.add_conditional_edges(
            "act",
            self._route_from_act,
            {
                "react": "react",
                "idle": "idle",
                "end": END
            }
        )

        workflow.add_conditional_edges(
            "react",
            self._route_from_react,
            {
                "idle": "idle",
                "reflect": "reflect",
                "end": END
            }
        )

        workflow.add_conditional_edges(
            "reflect",
            self._route_from_reflect,
            {
                "plan": "plan",
                "idle": "idle",
                "end": END
            }
        )

        return workflow.compile()

    # Phase Implementation Methods

    def _idle_phase(self, state: AgentState) -> Dict[str, Any]:
        """Idle phase - waiting for triggers or user input."""
        from .state import AgentPhase

        # Check if user explicitly requested planning (highest priority)
        if state.get("user_requested_planning"):
            return {
                "current_phase": AgentPhase.PLAN.value,
                "agent_reasoning": "User requested to create a new goal"
            }

        # Check if reflection suggested planning adjustments
        if state.get("reflection_suggests_planning"):
            return {
                "current_phase": AgentPhase.PLAN.value,
                "agent_reasoning": "Reflection analysis suggests planning adjustments needed"
            }

        # Check if there are no active goals (should create some)
        active_goals = self.memory.get_active_goals()
        if not active_goals:
            return {
                "current_phase": AgentPhase.PLAN.value,
                "agent_reasoning": "No active goals found, initiating goal creation"
            }

        # Check if there are scheduled tasks to execute
        scheduled_tasks = self._get_due_tasks()
        if scheduled_tasks:
            return {
                "current_phase": AgentPhase.ACT.value,
                "scheduled_tasks": scheduled_tasks,
                "agent_reasoning": f"Found {len(scheduled_tasks)} tasks due for execution"
            }

        # Check if reflection is due (weekly)
        if self._is_reflection_due():
            return {
                "current_phase": AgentPhase.REFLECT.value,
                "agent_reasoning": "Weekly reflection is due"
            }

        # Stay in idle
        return {
            "current_phase": AgentPhase.IDLE.value,
            "agent_reasoning": "No immediate actions required, staying in idle"
        }

    def _plan_phase(self, state: AgentState) -> Dict[str, Any]:
        """Planning phase - Agent uses LLM to intelligently plan and optimize tasks."""
        from .state import AgentPhase
        from langchain_core.messages import SystemMessage, HumanMessage

        try:
            # Step 1: Determine planning context
            planning_context = self._get_planning_context(state)

            # Step 2: Use LLM to analyze and plan
            plan_result = self._llm_generate_plan(planning_context)

            # Step 3: Execute the plan (create goals/tasks in memory)
            goal_id = self._execute_plan(plan_result)

            # Step 4: Present plan to human and get confirmation
            confirmation = self.human_tool.present_plan_for_confirmation(plan_result)

            # Step 5: Adjust based on human feedback if needed
            if confirmation.get("needs_adjustment"):
                adjusted_plan = self._adjust_plan_based_on_feedback(plan_result, confirmation)
                self._update_plan_in_memory(goal_id, adjusted_plan)

            return {
                "current_phase": AgentPhase.IDLE.value,
                "current_goal_id": goal_id,
                "agent_reasoning": f"Successfully planned and optimized goal with LLM intelligence",
                "agent_decision": "Planning complete, ready to execute tasks",
                "plan_summary": plan_result.get("summary", "Plan created"),
                "user_request_planning": False
            }

        except Exception as e:
            return {
                "current_phase": AgentPhase.IDLE.value,
                "error_message": f"Planning failed: {str(e)}",
                "agent_reasoning": "Planning encountered an error"
            }

    def _get_planning_context(self, state: AgentState) -> Dict[str, Any]:
        """Gather all relevant context for intelligent planning."""
        context = {
            "trigger_reason": "unknown",
            "historical_data": {},
            "user_preferences": {},
            "current_goals": [],
            "performance_patterns": {}
        }

        # Determine why planning was triggered
        if state.get("user_requested_planning"):
            context["trigger_reason"] = "user_request"
            # Get basic goal info from user
            goal_name, goal_description = self.human_tool.get_goal_details()
            context["target_goal"] = {"name": goal_name, "description": goal_description}

        elif state.get("reflection_suggests_planning"):
            context["trigger_reason"] = "reflection_optimization"
            context["reflection_insights"] = state.get("reflection_insights", {})

        elif not self.memory.get_active_goals():
            context["trigger_reason"] = "initial_setup"
            # Get basic goal info for new user
            goal_name, goal_description = self.human_tool.get_goal_details()
            context["target_goal"] = {"name": goal_name, "description": goal_description}

        # Gather historical performance data
        execution_logs = self.memory.get_execution_logs(days=30)
        context["historical_data"] = self._analyze_historical_performance(execution_logs)

        # Get current active goals for context
        context["current_goals"] = [
            {"name": g.name, "description": g.description}
            for g in self.memory.get_active_goals()
        ]

        return context

    def _llm_generate_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Use LLM to generate intelligent task breakdown and scheduling."""

        system_prompt = self._get_planning_system_prompt()
        user_prompt = self._build_planning_user_prompt(context)

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        response = self.llm.invoke(messages)

        # Parse LLM response into structured plan
        return self._parse_llm_plan_response(response.content)

    def _get_planning_system_prompt(self) -> str:
        """Get the system prompt for LLM planning."""
        return """You are an expert life coach and productivity agent. Your role is to:

1. Analyze the user's goal and break it down into actionable, specific tasks
2. Optimize task scheduling based on historical performance patterns
3. Consider the user's past success/failure patterns to maximize success probability
4. Create a realistic timeline that builds momentum and maintains motivation
5. Suggest optimal timing based on when the user has been most successful

You should respond with a structured plan in JSON format containing:
- goal_summary: Brief summary of the goal
- tasks: Array of specific, actionable tasks
- scheduling_strategy: Your reasoning for the proposed schedule
- success_optimization: How you've optimized for the user's success patterns
- estimated_timeline: Overall timeline for goal completion

Be intelligent, data-driven, and focused on maximizing the user's success probability."""

    def _build_planning_user_prompt(self, context: Dict[str, Any]) -> str:
        """Build the user prompt for LLM planning based on context."""
        prompt_parts = []

        # Add goal information
        if "target_goal" in context:
            goal = context["target_goal"]
            prompt_parts.append(f"GOAL: {goal['name']}")
            prompt_parts.append(f"DESCRIPTION: {goal['description']}")

        # Add trigger reason
        trigger_reason = context.get("trigger_reason", "unknown")
        if trigger_reason == "user_request":
            prompt_parts.append("\nCONTEXT: User is creating a new goal")
        elif trigger_reason == "reflection_optimization":
            prompt_parts.append("\nCONTEXT: Optimizing existing plan based on reflection insights")
            if context.get("reflection_insights"):
                insights = context["reflection_insights"]
                prompt_parts.append(f"REFLECTION INSIGHTS: {insights}")
        elif trigger_reason == "initial_setup":
            prompt_parts.append("\nCONTEXT: New user setting up their first goal")

        # Add historical performance data
        historical = context.get("historical_data", {})
        if historical:
            prompt_parts.append("\nHISTORICAL PERFORMANCE:")
            if "success_rate" in historical:
                prompt_parts.append(f"- Overall success rate: {historical['success_rate']:.1f}%")
            if "best_time_slots" in historical:
                prompt_parts.append(f"- Most successful times: {historical['best_time_slots']}")
            if "common_failures" in historical:
                prompt_parts.append(f"- Common failure reasons: {historical['common_failures']}")
            if "successful_patterns" in historical:
                prompt_parts.append(f"- Successful patterns: {historical['successful_patterns']}")

        # Add current goals for context
        current_goals = context.get("current_goals", [])
        if current_goals:
            prompt_parts.append(f"\nCURRENT ACTIVE GOALS: {len(current_goals)}")
            for goal in current_goals[:3]:  # Limit to 3 for brevity
                prompt_parts.append(f"- {goal['name']}")

        prompt_parts.append("\nPlease create an intelligent, optimized plan in JSON format.")

        return "\n".join(prompt_parts)

    def _parse_llm_plan_response(self, response_content: str) -> Dict[str, Any]:
        """Parse LLM response into structured plan."""
        import json
        import re

        # Try to extract JSON from the response
        json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
        if json_match:
            plan_data = json.loads(json_match.group())
            return plan_data
        else:
            # Fallback: parse as text and create structure
            return self._parse_text_plan_response(response_content)

    def _execute_plan(self, plan_result: Dict[str, Any]) -> int:
        """Execute the plan by creating goal and tasks in memory."""
        # Create the goal
        goal_summary = plan_result.get("goal_summary", "Generated Goal")
        goal_description = f"{goal_summary}\n\nStrategy: {plan_result.get('scheduling_strategy', '')}"

        goal_id = self.memory.create_goal(goal_summary, goal_description)

        # Create tasks
        tasks = plan_result.get("tasks", [])
        for task in tasks:
            self.memory.create_subtask(
                goal_id=goal_id,
                name=task.get("name", "Task"),
                description=task.get("description", "Task description"),
                trigger_frequency=task.get("frequency", "daily"),
                trigger_time=task.get("time", "09:00"),
                estimated_duration=task.get("duration", 30)
            )

        return goal_id

    def _analyze_historical_performance(self, execution_logs) -> Dict[str, Any]:
        """Analyze historical performance for planning insights."""
        if not execution_logs:
            return {}

        analysis = {}

        # Calculate success rate
        total = len(execution_logs)
        successful = sum(1 for log in execution_logs if log.status.value == "COMPLETED")
        analysis["success_rate"] = (successful / total) * 100 if total > 0 else 0

        # Analyze time patterns
        time_success = {}
        failure_reasons = {}

        for log in execution_logs:
            hour = log.triggered_at.hour
            time_slot = f"{hour:02d}:00"

            if time_slot not in time_success:
                time_success[time_slot] = {"success": 0, "total": 0}

            time_success[time_slot]["total"] += 1
            if log.status.value == "COMPLETED":
                time_success[time_slot]["success"] += 1

            # Track failure reasons
            if log.failure_reason_tag:
                failure_reasons[log.failure_reason_tag] = failure_reasons.get(log.failure_reason_tag, 0) + 1

        # Find best time slots
        best_times = []
        for time_slot, data in time_success.items():
            if data["total"] >= 2:  # Enough data
                success_rate = (data["success"] / data["total"]) * 100
                if success_rate >= 70:  # Good success rate
                    best_times.append(f"{time_slot} ({success_rate:.0f}%)")

        analysis["best_time_slots"] = best_times[:3]  # Top 3
        analysis["common_failures"] = list(failure_reasons.keys())[:3]  # Top 3

        # Identify successful patterns
        patterns = []
        if analysis["success_rate"] > 70:
            patterns.append("Generally consistent execution")
        if best_times:
            patterns.append(f"Strong performance in morning/evening slots")

        analysis["successful_patterns"] = patterns

        return analysis

    def _adjust_plan_based_on_feedback(self, original_plan: Dict[str, Any], feedback: Dict[str, Any]) -> Dict[str, Any]:
        """Adjust the plan based on human feedback."""
        adjusted_plan = original_plan.copy()

        feedback_text = feedback.get("adjustment_feedback", "")

        # Simple adjustment logic based on common feedback patterns
        if "time" in feedback_text.lower():
            # Adjust timing
            for task in adjusted_plan.get("tasks", []):
                if "morning" in feedback_text.lower():
                    task["time"] = "08:00"
                elif "evening" in feedback_text.lower():
                    task["time"] = "19:00"
                elif "afternoon" in feedback_text.lower():
                    task["time"] = "14:00"

        if "frequency" in feedback_text.lower() or "often" in feedback_text.lower():
            # Adjust frequency
            for task in adjusted_plan.get("tasks", []):
                if "less" in feedback_text.lower():
                    if task["frequency"] == "daily":
                        task["frequency"] = "weekly"
                elif "more" in feedback_text.lower():
                    if task["frequency"] == "weekly":
                        task["frequency"] = "daily"

        if "duration" in feedback_text.lower() or "time" in feedback_text.lower():
            # Adjust duration
            for task in adjusted_plan.get("tasks", []):
                if "shorter" in feedback_text.lower() or "less" in feedback_text.lower():
                    task["duration"] = max(15, task.get("duration", 30) - 15)
                elif "longer" in feedback_text.lower() or "more" in feedback_text.lower():
                    task["duration"] = min(120, task.get("duration", 30) + 15)

        adjusted_plan["adjustment_notes"] = f"Adjusted based on feedback: {feedback_text}"

        return adjusted_plan

    def _update_plan_in_memory(self, goal_id: int, adjusted_plan: Dict[str, Any]):
        """Update the plan in memory with adjustments."""
        # For now, we'll recreate the tasks
        # In a more sophisticated implementation, we'd update existing tasks

        # Get existing tasks for this goal
        existing_tasks = self.memory.get_active_subtasks(goal_id)

        # Update goal description with adjustment notes
        if "adjustment_notes" in adjusted_plan:
            # This would require adding an update_goal method to SystemMemory
            pass

        # For simplicity, we'll assume the tasks are already created and this is just logging
        # In a full implementation, you'd want to update the actual task records

    def _act_phase(self, state: AgentState) -> Dict[str, Any]:
        """Action phase - trigger task execution."""
        from .state import AgentPhase
        from .memory import ExecutionStatus

        try:
            # Get the first scheduled task
            scheduled_tasks = state.get("scheduled_tasks", [])
            if not scheduled_tasks:
                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "agent_reasoning": "No scheduled tasks to execute"
                }

            task = scheduled_tasks[0]

            # Create execution log
            log_id = self.memory.create_execution_log(
                subtask_id=task["id"],
                status=ExecutionStatus.TRIGGERED
            )

            # Trigger execution via human tool
            task_info = {
                "name": task["name"],
                "description": task["description"],
                "estimated_duration": task["estimated_duration"]
            }

            user_response = self.human_tool.trigger_execution(task_info)

            # Update execution log based on response
            if user_response == 'STARTED':
                status = ExecutionStatus.STARTED
            elif user_response == 'DEFERRED':
                status = ExecutionStatus.DEFERRED
            else:  # IGNORED
                status = ExecutionStatus.IGNORED

            self.memory.update_execution_log(log_id, status)

            return {
                "current_phase": AgentPhase.REACT.value,
                "current_task_id": task["id"],
                "current_execution_log_id": log_id,
                "human_responses": {"trigger_response": user_response},
                "agent_reasoning": f"Triggered task '{task['name']}', user responded: {user_response}"
            }

        except Exception as e:
            return {
                "current_phase": AgentPhase.IDLE.value,
                "error_message": f"Action phase failed: {str(e)}",
                "agent_reasoning": "Action phase encountered an error"
            }

    def _react_phase(self, state: AgentState) -> Dict[str, Any]:
        """React phase - process user responses and update status."""
        from .state import AgentPhase
        from .memory import ExecutionStatus

        try:
            log_id = state.get("current_execution_log_id")
            task_id = state.get("current_task_id")
            trigger_response = state.get("human_responses", {}).get("trigger_response")

            if trigger_response == 'STARTED':
                # Wait for completion (in real implementation, this would be time-based)
                # For now, immediately ask for result
                task_info = {"name": "Current Task"}  # Would get from memory
                status, feedback = self.human_tool.get_execution_result(task_info)

                if status == 'COMPLETED':
                    self.memory.update_execution_log(
                        log_id, ExecutionStatus.COMPLETED, user_feedback=feedback
                    )
                else:  # FAILED
                    failure_reason = self.human_tool.get_failure_reason(task_info)
                    self.memory.update_execution_log(
                        log_id, ExecutionStatus.FAILED,
                        failure_reason_tag=failure_reason, user_feedback=feedback
                    )

                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "agent_reasoning": f"Task completed with status: {status}"
                }

            elif trigger_response in ['DEFERRED', 'IGNORED']:
                # For failed triggers, get failure reason
                task_info = {"name": "Current Task"}
                failure_reason = self.human_tool.get_failure_reason(task_info)
                self.memory.update_execution_log(
                    log_id, ExecutionStatus.FAILED if trigger_response == 'IGNORED' else ExecutionStatus.DEFERRED,
                    failure_reason_tag=failure_reason
                )

                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "agent_reasoning": f"Task {trigger_response.lower()}, reason: {failure_reason}"
                }

        except Exception as e:
            return {
                "current_phase": AgentPhase.IDLE.value,
                "error_message": f"React phase failed: {str(e)}",
                "agent_reasoning": "React phase encountered an error"
            }

    def _reflect_phase(self, state: AgentState) -> Dict[str, Any]:
        """Reflect phase - analyze patterns and generate insights."""
        from .state import AgentPhase

        try:
            # Get execution logs from the past week
            logs = self.memory.get_execution_logs(days=7)

            if not logs:
                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "agent_reasoning": "No execution data to analyze"
                }

            # Analyze patterns
            analysis = self._analyze_execution_patterns(logs)

            # Generate reflection report
            report = {
                "success_rate": analysis.get("success_rate", 0),
                "common_failure": analysis.get("most_common_failure", "N/A"),
                "best_time": analysis.get("best_time_slot", "N/A"),
                "recommendations": analysis.get("recommendations", "None")
            }

            # Present to user
            user_response = self.human_tool.present_reflection_report(report)

            # Store insight
            self.memory.create_insight(
                insight_type="weekly_report",
                content=f"Weekly reflection completed. User response: {user_response}",
                data={"report": report, "user_response": user_response}
            )

            # If user wants to adjust, signal idle phase to trigger planning
            if user_response.lower() in ['yes', 'y']:
                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "reflection_suggests_planning": True,  # Signal to idle phase
                    "agent_reasoning": "User wants to adjust schedule, will trigger planning phase"
                }
            else:
                return {
                    "current_phase": AgentPhase.IDLE.value,
                    "agent_reasoning": "Reflection complete, user satisfied with current schedule"
                }

        except Exception as e:
            return {
                "current_phase": AgentPhase.IDLE.value,
                "error_message": f"Reflect phase failed: {str(e)}",
                "agent_reasoning": "Reflect phase encountered an error"
            }

    # Routing Methods

    def _route_from_idle(self, state: AgentState) -> str:
        """Route from idle phase."""
        # The idle phase updates the current_phase based on what should happen next
        next_phase = state.get("current_phase")

        if next_phase == "ACT":
            return "act"
        elif next_phase == "REFLECT":
            return "reflect"
        elif next_phase == "PLAN":
            return "plan"
        elif not state.get("should_continue", True):
            return "end"
        else:
            # Stay in idle if nothing to do
            return "idle"

    def _route_from_plan(self, state: AgentState) -> str:
        """Route from plan phase."""
        if state.get("error_message"):
            return "end"
        return "idle"

    def _route_from_act(self, state: AgentState) -> str:
        """Route from act phase."""
        if state.get("error_message"):
            return "idle"
        return "react"

    def _route_from_react(self, state: AgentState) -> str:
        """Route from react phase."""
        if state.get("error_message"):
            return "idle"
        # Check if reflection is needed after multiple failures
        return "idle"

    def _route_from_reflect(self, state: AgentState) -> str:
        """Route from reflect phase."""
        current_phase = state.get("current_phase")
        if current_phase == "PLAN":
            return "plan"
        return "idle"

    # Helper Methods

    def _get_due_tasks(self) -> List[Dict[str, Any]]:
        """Get tasks that are due for execution."""
        from datetime import datetime, time

        # Get all active subtasks
        subtasks = self.memory.get_active_subtasks()
        due_tasks = []

        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute
        current_weekday = current_time.weekday()  # 0=Monday, 6=Sunday

        for task in subtasks:
            # Parse trigger time (HH:MM format)
            try:
                trigger_hour, trigger_minute = map(int, task.trigger_time.split(':'))

                # Check if task is due based on frequency
                is_due = False

                if task.trigger_frequency == "daily":
                    # For demo purposes, consider task due if current time matches or is past trigger time
                    if (current_hour > trigger_hour or
                        (current_hour == trigger_hour and current_minute >= trigger_minute)):
                        is_due = True

                elif task.trigger_frequency == "weekly":
                    # For demo, trigger on Mondays
                    if current_weekday == 0 and (current_hour > trigger_hour or
                        (current_hour == trigger_hour and current_minute >= trigger_minute)):
                        is_due = True

                elif task.trigger_frequency == "hourly":
                    # For demo, trigger every hour
                    if current_minute >= trigger_minute:
                        is_due = True

                if is_due:
                    due_tasks.append({
                        "id": task.id,
                        "name": task.name,
                        "description": task.description,
                        "estimated_duration": task.estimated_duration,
                        "trigger_time": task.trigger_time,
                        "frequency": task.trigger_frequency
                    })

            except ValueError:
                # Skip tasks with invalid time format
                continue

        return due_tasks

    def _is_reflection_due(self) -> bool:
        """Check if weekly reflection is due."""
        from datetime import datetime, timedelta

        # Check if we have any execution logs in the past week
        logs = self.memory.get_execution_logs(days=7)

        # For demo purposes, trigger reflection if:
        # 1. We have at least 3 execution logs, OR
        # 2. It's Sunday (weekday 6), OR
        # 3. We have multiple failures in recent logs

        current_time = datetime.now()

        # Check if it's Sunday (reflection day)
        if current_time.weekday() == 6:  # Sunday
            return True

        # Check if we have enough data for reflection
        if len(logs) >= 3:
            # Check failure rate
            failed_logs = [log for log in logs if log.status.value in ["FAILED", "IGNORED"]]
            if len(failed_logs) >= 2:  # Multiple failures trigger reflection
                return True

        return False

    def _check_historical_patterns(self, trigger_time: str) -> Optional[str]:
        """Check historical patterns for insights."""
        # Get execution logs from the past month
        logs = self.memory.get_execution_logs(days=30)

        if not logs:
            return None

        # Analyze patterns around the proposed trigger time
        try:
            trigger_hour = int(trigger_time.split(':')[0])

            # Count successes and failures by time slots
            time_slots = {}
            for log in logs:
                log_hour = log.triggered_at.hour
                slot = f"{log_hour:02d}:00"

                if slot not in time_slots:
                    time_slots[slot] = {"success": 0, "failure": 0}

                if log.status.value == "COMPLETED":
                    time_slots[slot]["success"] += 1
                else:
                    time_slots[slot]["failure"] += 1

            # Check if the proposed time has low success rate
            proposed_slot = f"{trigger_hour:02d}:00"
            if proposed_slot in time_slots:
                slot_data = time_slots[proposed_slot]
                total = slot_data["success"] + slot_data["failure"]
                success_rate = (slot_data["success"] / total) * 100 if total > 0 else 0

                if success_rate < 50 and total >= 3:  # Low success rate with enough data
                    # Find better time slot
                    best_slot = None
                    best_rate = 0

                    for slot, data in time_slots.items():
                        slot_total = data["success"] + data["failure"]
                        if slot_total >= 2:  # Enough data
                            rate = (data["success"] / slot_total) * 100
                            if rate > best_rate:
                                best_rate = rate
                                best_slot = slot

                    if best_slot and best_rate > success_rate + 20:  # Significantly better
                        return f"I notice tasks scheduled at {trigger_time} have a {success_rate:.0f}% success rate. " \
                               f"Tasks at {best_slot} have been {best_rate:.0f}% successful. " \
                               f"Would you like to schedule this task at {best_slot} instead?"

        except (ValueError, IndexError):
            pass

        return None

    def _analyze_execution_patterns(self, logs) -> Dict[str, Any]:
        """Analyze execution patterns from logs."""
        if not logs:
            return {"success_rate": 0}

        total = len(logs)
        successful = sum(1 for log in logs if log.status.value == "COMPLETED")
        success_rate = (successful / total) * 100

        # Count failure reasons
        failure_reasons = {}
        for log in logs:
            if log.failure_reason_tag:
                failure_reasons[log.failure_reason_tag] = failure_reasons.get(log.failure_reason_tag, 0) + 1

        most_common_failure = max(failure_reasons.items(), key=lambda x: x[1])[0] if failure_reasons else "None"

        return {
            "success_rate": success_rate,
            "most_common_failure": most_common_failure,
            "best_time_slot": "09:00",  # Would analyze actual time patterns
            "recommendations": f"Consider addressing {most_common_failure} issues" if most_common_failure != "None" else "Keep up the good work!"
        }

    # Public Interface Methods

    def create_goal(self) -> str:
        """Start the planning phase to create a new goal."""
        from .state import AgentPhase

        initial_state = {
            "current_phase": AgentPhase.IDLE.value,
            "user_requested_planning": True,  # Signal to idle phase to trigger planning
            "loop_iteration": 0,
            "messages": [],
            "human_responses": {},
            "agent_reasoning": "User requested to create a new goal",
            "agent_decision": "Starting planning phase",
            "scheduled_tasks": [],
            "analysis_results": {},
            "insights": [],
            "should_continue": True,
            "error_message": None
        }

        try:
            result = self.graph.invoke(initial_state)
            return result.get("agent_reasoning", "Goal creation completed")
        except Exception as e:
            return f"Error creating goal: {str(e)}"

    def trigger_scheduled_task(self, task_id: int) -> str:
        """Trigger execution of a scheduled task."""
        from .state import AgentPhase

        # Get task details from memory
        subtasks = self.memory.get_active_subtasks()
        task = next((t for t in subtasks if t.id == task_id), None)

        if not task:
            return f"Task {task_id} not found"

        initial_state = {
            "current_phase": AgentPhase.ACT.value,
            "loop_iteration": 0,
            "messages": [],
            "human_responses": {},
            "scheduled_tasks": [{
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "estimated_duration": task.estimated_duration
            }],
            "agent_reasoning": f"Triggering scheduled task: {task.name}",
            "should_continue": True
        }

        try:
            result = self.graph.invoke(initial_state)
            return result.get("agent_reasoning", "Task execution completed")
        except Exception as e:
            return f"Error executing task: {str(e)}"

    def run_reflection(self) -> str:
        """Run the weekly reflection process."""
        from .state import AgentPhase

        initial_state = {
            "current_phase": AgentPhase.REFLECT.value,
            "loop_iteration": 0,
            "messages": [],
            "human_responses": {},
            "agent_reasoning": "Starting weekly reflection",
            "should_continue": True
        }

        try:
            result = self.graph.invoke(initial_state)
            return result.get("agent_reasoning", "Reflection completed")
        except Exception as e:
            return f"Error during reflection: {str(e)}"

    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and statistics."""
        goals = self.memory.get_active_goals()
        subtasks = self.memory.get_active_subtasks()
        recent_logs = self.memory.get_execution_logs(days=7)

        # Calculate success rate
        if recent_logs:
            successful = sum(1 for log in recent_logs if log.status.value == "COMPLETED")
            success_rate = (successful / len(recent_logs)) * 100
        else:
            success_rate = 0

        return {
            "active_goals": len(goals),
            "active_tasks": len(subtasks),
            "recent_executions": len(recent_logs),
            "success_rate": success_rate,
            "goals": [{"id": g.id, "name": g.name} for g in goals],
            "tasks": [{"id": t.id, "name": t.name, "frequency": t.trigger_frequency} for t in subtasks]
        }


def create_agent(cli_interface=None) -> LifeAgent:
    """Create and return a Life Agent instance."""
    return LifeAgent(cli_interface)
