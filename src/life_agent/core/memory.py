# -*- coding: utf-8 -*-
"""System Memory - The persistent database for the Life Agent."""

import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass


class GoalStatus(Enum):
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    PAUSED = "PAUSED"
    CANCELLED = "CANCELLED"


class TaskStatus(Enum):
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    PAUSED = "PAUSED"
    CANCELLED = "CANCELLED"


class ExecutionStatus(Enum):
    TRIGGERED = "TRIGGERED"
    STARTED = "STARTED"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    DEFERRED = "DEFERRED"
    IGNORED = "IGNORED"


@dataclass
class Goal:
    id: Optional[int]
    name: str
    description: str
    status: GoalStatus
    created_at: datetime
    updated_at: datetime


@dataclass
class SubTask:
    id: Optional[int]
    goal_id: int
    name: str
    description: str
    trigger_frequency: str  # daily, weekly, etc.
    trigger_time: str  # HH:MM format
    estimated_duration: int  # minutes
    status: TaskStatus
    created_at: datetime
    updated_at: datetime


@dataclass
class ExecutionLog:
    id: Optional[int]
    subtask_id: int
    triggered_at: datetime
    status: ExecutionStatus
    failure_reason_tag: Optional[str]
    user_feedback: Optional[str]
    completed_at: Optional[datetime]


@dataclass
class Insight:
    id: Optional[int]
    type: str  # weekly_report, pattern_analysis, etc.
    content: str
    data: Dict[str, Any]  # JSON data
    created_at: datetime
    applied: bool


class SystemMemory:
    """Persistent storage system for the Life Agent."""
    
    def __init__(self, db_path: str = "life_agent.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize the database schema."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS subtasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    goal_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    trigger_frequency TEXT NOT NULL,
                    trigger_time TEXT NOT NULL,
                    estimated_duration INTEGER DEFAULT 30,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (goal_id) REFERENCES goals (id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS execution_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    subtask_id INTEGER NOT NULL,
                    triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT NOT NULL,
                    failure_reason_tag TEXT,
                    user_feedback TEXT,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (subtask_id) REFERENCES subtasks (id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    data TEXT,  -- JSON
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    applied BOOLEAN DEFAULT FALSE
                )
            """)
            
            conn.commit()
    
    def create_goal(self, name: str, description: str) -> int:
        """Create a new goal and return its ID."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "INSERT INTO goals (name, description, status) VALUES (?, ?, ?)",
                (name, description, GoalStatus.ACTIVE.value)
            )
            return cursor.lastrowid
    
    def create_subtask(self, goal_id: int, name: str, description: str, 
                      trigger_frequency: str, trigger_time: str, 
                      estimated_duration: int = 30) -> int:
        """Create a new subtask and return its ID."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                """INSERT INTO subtasks 
                   (goal_id, name, description, trigger_frequency, trigger_time, estimated_duration, status) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (goal_id, name, description, trigger_frequency, trigger_time, 
                 estimated_duration, TaskStatus.ACTIVE.value)
            )
            return cursor.lastrowid
    
    def create_execution_log(self, subtask_id: int, status: ExecutionStatus) -> int:
        """Create a new execution log and return its ID."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "INSERT INTO execution_logs (subtask_id, status) VALUES (?, ?)",
                (subtask_id, status.value)
            )
            return cursor.lastrowid
    
    def update_execution_log(self, log_id: int, status: ExecutionStatus, 
                           failure_reason_tag: Optional[str] = None,
                           user_feedback: Optional[str] = None):
        """Update an execution log."""
        with sqlite3.connect(self.db_path) as conn:
            completed_at = datetime.now() if status in [ExecutionStatus.COMPLETED, ExecutionStatus.FAILED] else None
            conn.execute(
                """UPDATE execution_logs 
                   SET status = ?, failure_reason_tag = ?, user_feedback = ?, completed_at = ?
                   WHERE id = ?""",
                (status.value, failure_reason_tag, user_feedback, completed_at, log_id)
            )
            conn.commit()
    
    def get_active_goals(self) -> List[Goal]:
        """Get all active goals."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                "SELECT * FROM goals WHERE status = ? ORDER BY created_at DESC",
                (GoalStatus.ACTIVE.value,)
            )
            return [self._row_to_goal(row) for row in cursor.fetchall()]
    
    def get_active_subtasks(self, goal_id: Optional[int] = None) -> List[SubTask]:
        """Get active subtasks, optionally filtered by goal."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            if goal_id:
                cursor = conn.execute(
                    "SELECT * FROM subtasks WHERE goal_id = ? AND status = ? ORDER BY created_at",
                    (goal_id, TaskStatus.ACTIVE.value)
                )
            else:
                cursor = conn.execute(
                    "SELECT * FROM subtasks WHERE status = ? ORDER BY created_at",
                    (TaskStatus.ACTIVE.value,)
                )
            return [self._row_to_subtask(row) for row in cursor.fetchall()]
    
    def get_execution_logs(self, days: int = 7) -> List[ExecutionLog]:
        """Get execution logs from the last N days."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                """SELECT * FROM execution_logs 
                   WHERE triggered_at >= datetime('now', '-{} days')
                   ORDER BY triggered_at DESC""".format(days)
            )
            return [self._row_to_execution_log(row) for row in cursor.fetchall()]
    
    def create_insight(self, insight_type: str, content: str, data: Dict[str, Any]) -> int:
        """Create a new insight."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "INSERT INTO insights (type, content, data) VALUES (?, ?, ?)",
                (insight_type, content, json.dumps(data))
            )
            return cursor.lastrowid
    
    def _row_to_goal(self, row) -> Goal:
        """Convert database row to Goal object."""
        return Goal(
            id=row['id'],
            name=row['name'],
            description=row['description'],
            status=GoalStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at'])
        )
    
    def _row_to_subtask(self, row) -> SubTask:
        """Convert database row to SubTask object."""
        return SubTask(
            id=row['id'],
            goal_id=row['goal_id'],
            name=row['name'],
            description=row['description'],
            trigger_frequency=row['trigger_frequency'],
            trigger_time=row['trigger_time'],
            estimated_duration=row['estimated_duration'],
            status=TaskStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at'])
        )
    
    def _row_to_execution_log(self, row) -> ExecutionLog:
        """Convert database row to ExecutionLog object."""
        return ExecutionLog(
            id=row['id'],
            subtask_id=row['subtask_id'],
            triggered_at=datetime.fromisoformat(row['triggered_at']),
            status=ExecutionStatus(row['status']),
            failure_reason_tag=row['failure_reason_tag'],
            user_feedback=row['user_feedback'],
            completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None
        )
