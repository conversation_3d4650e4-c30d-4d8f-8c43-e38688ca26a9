# -*- coding: utf-8 -*-
"""Main CLI interface for the Life Agent."""

import click
import sys
from ..core.agent import create_agent
from ..core.config import get_config


class CLIInterface:
    """CLI interface wrapper for human tool interactions."""

    def prompt(self, message: str) -> str:
        """Prompt user for input."""
        return click.prompt(message, type=str)

    def echo(self, message: str):
        """Display message to user."""
        click.echo(message)


@click.group()
@click.version_option(version="0.1.0")
def cli():
    """Life Agent - Your AI assistant for life management."""
    pass


@cli.command()
def create_goal():
    """Create a new goal with the Life Agent."""
    try:
        # Validate configuration
        get_config()  # This will raise ValueError if config is invalid

        # Create CLI interface wrapper
        cli_interface = CLIInterface()
        agent = create_agent(cli_interface)

        click.echo("=== Creating New Goal ===")
        result = agent.create_goal()
        click.echo(f"\nResult: {result}")

    except ValueError as e:
        click.echo(f"Configuration Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('task_id', type=int)
def execute_task(task_id):
    """Execute a scheduled task."""
    try:
        get_config()  # Validate configuration
        cli_interface = CLIInterface()
        agent = create_agent(cli_interface)

        click.echo(f"=== Executing Task {task_id} ===")
        result = agent.trigger_scheduled_task(task_id)
        click.echo(f"\nResult: {result}")

    except ValueError as e:
        click.echo(f"Configuration Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
def reflect():
    """Run weekly reflection process."""
    try:
        get_config()  # Validate configuration
        cli_interface = CLIInterface()
        agent = create_agent(cli_interface)

        click.echo("=== Weekly Reflection ===")
        result = agent.run_reflection()
        click.echo(f"\nResult: {result}")

    except ValueError as e:
        click.echo(f"Configuration Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
def status():
    """Show agent status and statistics."""
    try:
        get_config()  # Validate configuration
        agent = create_agent()

        status_info = agent.get_status()

        click.echo("=== Agent Status ===")
        click.echo(f"Active Goals: {status_info['active_goals']}")
        click.echo(f"Active Tasks: {status_info['active_tasks']}")
        click.echo(f"Recent Executions (7 days): {status_info['recent_executions']}")
        click.echo(f"Success Rate: {status_info['success_rate']:.1f}%")

        if status_info['goals']:
            click.echo("\nActive Goals:")
            for goal in status_info['goals']:
                click.echo(f"  - {goal['name']} (ID: {goal['id']})")

        if status_info['tasks']:
            click.echo("\nActive Tasks:")
            for task in status_info['tasks']:
                click.echo(f"  - {task['name']} ({task['frequency']}) (ID: {task['id']})")

    except ValueError as e:
        click.echo(f"Configuration Error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
def config():
    """Show current configuration."""
    try:
        config = get_config()
        click.echo("Current Configuration:")
        click.echo(f"  Model: {config.model}")
        click.echo(f"  Temperature: {config.temperature}")
        click.echo(f"  Max Tokens: {config.max_tokens}")
        click.echo(f"  OpenAI API Key: {'Set' if config.openai_api_key else 'Not Set'}")
        click.echo(f"  LangSmith Tracing: {'Enabled' if config.langchain_tracing_v2 else 'Disabled'}")
        if config.langchain_tracing_v2:
            click.echo(f"  LangSmith Project: {config.langchain_project}")
    except Exception as e:
        click.echo(f"Error reading configuration: {e}", err=True)


@cli.command()
def help_commands():
    """Show available commands and their usage."""
    click.echo("Life Agent Commands:")
    click.echo("  create-goal     - Create a new goal with tasks")
    click.echo("  execute-task ID - Execute a specific task")
    click.echo("  reflect         - Run weekly reflection process")
    click.echo("  status          - Show agent status and statistics")
    click.echo("  config          - Show current configuration")
    click.echo("\nThe Life Agent operates on a Plan-Act-React-Reflect cycle:")
    click.echo("  PLAN: Create goals and break them into actionable tasks")
    click.echo("  ACT: Trigger task execution at scheduled times")
    click.echo("  REACT: Process your responses and update execution status")
    click.echo("  REFLECT: Analyze patterns and generate insights for improvement")


if __name__ == '__main__':
    cli()
