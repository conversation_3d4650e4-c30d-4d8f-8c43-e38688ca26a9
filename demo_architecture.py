#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Architecture demo for the Life Agent - shows the system design without requiring API keys."""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from life_agent.core.memory import SystemMemory, GoalStatus, TaskStatus, ExecutionStatus


def demo_system_memory():
    """Demonstrate the System Memory component."""
    print("\n" + "="*60)
    print("SYSTEM MEMORY DEMO")
    print("="*60)
    
    # Create memory instance
    memory = SystemMemory("demo.db")
    
    print("1. Creating a goal...")
    goal_id = memory.create_goal(
        name="Learn Python Programming",
        description="Master Python fundamentals and build projects"
    )
    print(f"   Created goal with ID: {goal_id}")
    
    print("\n2. Creating subtasks...")
    task_ids = []
    tasks = [
        ("Read Python tutorial", "Complete online Python tutorial", "daily", "09:00", 60),
        ("Practice coding", "Solve coding exercises", "daily", "19:00", 45),
        ("Build a project", "Create a small Python project", "weekly", "10:00", 120)
    ]
    
    for name, desc, freq, time, duration in tasks:
        task_id = memory.create_subtask(
            goal_id=goal_id,
            name=name,
            description=desc,
            trigger_frequency=freq,
            trigger_time=time,
            estimated_duration=duration
        )
        task_ids.append(task_id)
        print(f"   Created task: {name} (ID: {task_id})")
    
    print("\n3. Simulating task executions...")
    for i, task_id in enumerate(task_ids):
        # Create execution log
        log_id = memory.create_execution_log(task_id, ExecutionStatus.TRIGGERED)
        
        # Simulate different outcomes
        if i == 0:  # Success
            memory.update_execution_log(log_id, ExecutionStatus.COMPLETED, user_feedback="Great session!")
            print(f"   Task {task_id}: COMPLETED")
        elif i == 1:  # Failure
            memory.update_execution_log(log_id, ExecutionStatus.FAILED, 
                                      failure_reason_tag="no_time", 
                                      user_feedback="Too busy today")
            print(f"   Task {task_id}: FAILED (no_time)")
        else:  # Deferred
            memory.update_execution_log(log_id, ExecutionStatus.DEFERRED, 
                                      failure_reason_tag="not_motivated")
            print(f"   Task {task_id}: DEFERRED (not_motivated)")
    
    print("\n4. Creating insights...")
    insight_id = memory.create_insight(
        insight_type="weekly_report",
        content="Success rate is 33%. Most common failure: no_time. Recommend scheduling tasks earlier.",
        data={
            "success_rate": 33.3,
            "total_executions": 3,
            "successful_executions": 1,
            "most_common_failure": "no_time",
            "recommendation": "Schedule tasks earlier in the day"
        }
    )
    print(f"   Created insight with ID: {insight_id}")
    
    print("\n5. Querying data...")
    goals = memory.get_active_goals()
    print(f"   Active goals: {len(goals)}")
    for goal in goals:
        print(f"     - {goal.name} (created: {goal.created_at.strftime('%Y-%m-%d %H:%M')})")
    
    subtasks = memory.get_active_subtasks()
    print(f"   Active tasks: {len(subtasks)}")
    for task in subtasks:
        print(f"     - {task.name} ({task.trigger_frequency} at {task.trigger_time})")
    
    logs = memory.get_execution_logs(days=7)
    print(f"   Recent execution logs: {len(logs)}")
    for log in logs:
        print(f"     - Task {log.subtask_id}: {log.status.value}")
        if log.failure_reason_tag:
            print(f"       Reason: {log.failure_reason_tag}")
    
    # Clean up
    os.remove("demo.db")
    print("\n   Demo database cleaned up.")


def demo_human_tool_interface():
    """Demonstrate the Human Tool Interface concept."""
    print("\n" + "="*60)
    print("HUMAN TOOL INTERFACE DEMO")
    print("="*60)
    
    print("The Human Tool Interface treats YOU as the Agent's primary tool.")
    print("Here's how the Agent would interact with you:\n")
    
    print("AGENT CALLS: get_goal_details()")
    print("  → Sends notification: 'What goal would you like to work on?'")
    print("  → Waits for your response")
    print("  → Returns: ('Learn Spanish', 'Become conversational in 6 months')")
    
    print("\nAGENT CALLS: get_sub_tasks('Learn Spanish')")
    print("  → Sends notification: 'Break down your Spanish learning goal'")
    print("  → Guides you through task creation")
    print("  → Returns: [{'name': 'Daily vocabulary', 'description': '10 new words'}, ...]")
    
    print("\nAGENT CALLS: get_task_trigger('Daily vocabulary')")
    print("  → Asks: 'When should I remind you to study vocabulary?'")
    print("  → Returns: {'frequency': 'daily', 'time': '08:00', 'duration': '15'}")
    
    print("\nAGENT CALLS: trigger_execution({'name': 'Daily vocabulary', ...})")
    print("  → Sends push notification: 'Time to study vocabulary!'")
    print("  → Returns: 'STARTED' (user clicked 'Start')")
    
    print("\nAGENT CALLS: get_execution_result({'name': 'Daily vocabulary'})")
    print("  → After estimated time, asks: 'How did vocabulary study go?'")
    print("  → Returns: ('COMPLETED', 'Learned 12 words today!')")
    
    print("\nAGENT CALLS: present_reflection_report({...})")
    print("  → Weekly: 'Your success rate is 85%. Best time: 8 AM. Adjust schedule?'")
    print("  → Returns: 'yes' (user wants to optimize)")


def demo_agent_phases():
    """Demonstrate the four phases of the Agent's main loop."""
    print("\n" + "="*60)
    print("AGENT PHASES DEMO")
    print("="*60)
    
    print("The Life Agent operates on a continuous Plan-Act-React-Reflect cycle:\n")
    
    print("🎯 PLAN PHASE")
    print("   Triggered by: User creates new goal")
    print("   Agent actions:")
    print("   1. Calls human_tool.get_goal_details()")
    print("   2. Calls human_tool.get_sub_tasks(goal_name)")
    print("   3. For each task: human_tool.get_task_trigger(task_name)")
    print("   4. Checks historical patterns for insights")
    print("   5. Stores everything in System Memory")
    print("   Result: Goal and tasks created, scheduling optimized")
    
    print("\n⚡ ACT PHASE")
    print("   Triggered by: Scheduled time reached")
    print("   Agent actions:")
    print("   1. Identifies due task from schedule")
    print("   2. Creates execution log (status: TRIGGERED)")
    print("   3. Calls human_tool.trigger_execution(task_info)")
    print("   4. Updates log based on user response")
    print("   Result: Task execution initiated")
    
    print("\n🔄 REACT PHASE")
    print("   Triggered by: User response to task trigger")
    print("   Agent actions:")
    print("   1. If STARTED: Wait, then call get_execution_result()")
    print("   2. If COMPLETED: Update log with success")
    print("   3. If FAILED/IGNORED: Call get_failure_reason()")
    print("   4. Store all feedback in execution log")
    print("   Result: Execution outcome recorded")
    
    print("\n🧠 REFLECT PHASE")
    print("   Triggered by: Weekly schedule OR multiple failures")
    print("   Agent actions:")
    print("   1. Query execution logs from past week")
    print("   2. Analyze patterns (success rate, failure reasons, timing)")
    print("   3. Generate insights and recommendations")
    print("   4. Call human_tool.present_reflection_report(report)")
    print("   5. If user agrees, trigger new PLAN phase")
    print("   Result: Continuous improvement and optimization")


def demo_data_flow():
    """Demonstrate how data flows through the system."""
    print("\n" + "="*60)
    print("DATA FLOW DEMO")
    print("="*60)
    
    print("Data flows through three core components:\n")
    
    print("📊 SYSTEM MEMORY (SQLite Database)")
    print("   Tables:")
    print("   - goals: Goal definitions and status")
    print("   - subtasks: Task breakdown with scheduling")
    print("   - execution_logs: Complete execution history")
    print("   - insights: Generated recommendations")
    print("   Purpose: Persistent storage and pattern analysis")
    
    print("\n🤖 AGENT CORE (LangGraph Workflow)")
    print("   States: PLAN → ACT → REACT → REFLECT → (loop)")
    print("   Functions:")
    print("   - Decision making and routing")
    print("   - Pattern analysis and insight generation")
    print("   - Scheduling and timing optimization")
    print("   - Learning from historical data")
    
    print("\n👤 HUMAN TOOL INTERFACE")
    print("   Methods:")
    print("   - get_goal_details() → goal definition")
    print("   - get_sub_tasks() → task breakdown")
    print("   - trigger_execution() → task reminders")
    print("   - get_execution_result() → completion status")
    print("   - present_reflection_report() → insights delivery")
    print("   Purpose: Agent's primary tool for achieving goals")
    
    print("\n🔄 TYPICAL DATA FLOW:")
    print("   1. User input → Human Tool → Agent Core")
    print("   2. Agent Core → System Memory (store)")
    print("   3. Agent Core → System Memory (query patterns)")
    print("   4. Agent Core → Human Tool (trigger actions)")
    print("   5. Human Tool → Agent Core (user responses)")
    print("   6. Agent Core → System Memory (update logs)")
    print("   7. System Memory → Agent Core (analysis data)")
    print("   8. Agent Core → Human Tool (insights)")


def main():
    """Run the complete architecture demo."""
    print("Life Agent Architecture Demo")
    print("Demonstrating the revolutionary goal-driven AI system")
    print("that treats humans as tools to achieve long-term success.")
    
    try:
        demo_system_memory()
        demo_human_tool_interface()
        demo_agent_phases()
        demo_data_flow()
        
        print("\n" + "="*60)
        print("ARCHITECTURE DEMO COMPLETE")
        print("="*60)
        print("\nKey Innovations Demonstrated:")
        print("✓ Human-as-Tool paradigm")
        print("✓ Autonomous Plan-Act-React-Reflect cycle")
        print("✓ Persistent learning and pattern analysis")
        print("✓ Data-driven optimization")
        print("✓ Continuous goal achievement system")
        
        print("\nTo see the agent in action (requires OpenAI API key):")
        print("  1. Set OPENAI_API_KEY in .env file")
        print("  2. python3 main.py create-goal")
        print("  3. python3 main.py status")
        print("  4. python3 main.py execute-task <id>")
        print("  5. python3 main.py reflect")
        
    except Exception as e:
        print(f"\nDemo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
