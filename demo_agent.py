#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Demo script for the Life Agent - shows the Plan-Act-React-Reflect cycle."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from life_agent.core.agent import create_agent
from life_agent.core.memory import SystemMemory


class MockCLIInterface:
    """Mock CLI interface for demonstration."""
    
    def __init__(self):
        self.responses = {
            "goal_name": "Learn Python Programming",
            "goal_description": "Master Python programming fundamentals and build real projects",
            "tasks": [
                {"name": "Read Python tutorial", "description": "Complete online Python tutorial"},
                {"name": "Practice coding", "description": "Solve coding exercises daily"},
                {"name": "Build a project", "description": "Create a small Python project"}
            ],
            "triggers": {
                "Read Python tutorial": {"frequency": "daily", "time": "09:00", "duration": "60"},
                "Practice coding": {"frequency": "daily", "time": "19:00", "duration": "45"},
                "Build a project": {"frequency": "weekly", "time": "10:00", "duration": "120"}
            },
            "execution_responses": ["yes", "completed", ""],
            "failure_reasons": ["no_time", "forgot", "not_motivated"],
            "reflection_response": "yes"
        }
        self.call_count = 0
    
    def prompt(self, message: str) -> str:
        """Mock prompt responses."""
        print(f"[USER PROMPT] {message}")
        
        if "goal" in message.lower() and "name" in message.lower():
            response = self.responses["goal_name"]
        elif "describe" in message.lower():
            response = self.responses["goal_description"]
        elif "task name" in message.lower():
            if self.call_count < len(self.responses["tasks"]):
                response = self.responses["tasks"][self.call_count]["name"]
                self.call_count += 1
            else:
                response = "done"
        elif "describe the task" in message.lower():
            # Find the task being described
            for task in self.responses["tasks"]:
                if task["name"] in message:
                    response = task["description"]
                    break
            else:
                response = "Task description"
        elif "frequency" in message.lower():
            # Extract task name from message
            task_name = None
            for task in self.responses["tasks"]:
                if task["name"].lower() in message.lower():
                    task_name = task["name"]
                    break
            if task_name and task_name in self.responses["triggers"]:
                response = self.responses["triggers"][task_name]["frequency"]
            else:
                response = "daily"
        elif "time" in message.lower() and "format" in message.lower():
            task_name = None
            for task in self.responses["tasks"]:
                if task["name"].lower() in message.lower():
                    task_name = task["name"]
                    break
            if task_name and task_name in self.responses["triggers"]:
                response = self.responses["triggers"][task_name]["time"]
            else:
                response = "09:00"
        elif "duration" in message.lower():
            task_name = None
            for task in self.responses["tasks"]:
                if task["name"].lower() in message.lower():
                    task_name = task["name"]
                    break
            if task_name and task_name in self.responses["triggers"]:
                response = self.responses["triggers"][task_name]["duration"]
            else:
                response = "30"
        elif "start this task" in message.lower():
            response = "yes"
        elif "how did" in message.lower() or "status" in message.lower():
            response = "completed"
        elif "adjust" in message.lower():
            response = self.responses["reflection_response"]
        else:
            response = "yes"
        
        print(f"[USER RESPONSE] {response}")
        return response
    
    def echo(self, message: str):
        """Mock echo."""
        print(f"[AGENT] {message}")


def demo_plan_phase():
    """Demonstrate the Planning phase."""
    print("\n" + "="*60)
    print("DEMO: PLAN PHASE - Creating a Goal")
    print("="*60)
    
    cli_interface = MockCLIInterface()
    agent = create_agent(cli_interface)
    
    print("\nAgent is starting the planning phase...")
    result = agent.create_goal()
    print(f"\nPlanning Result: {result}")
    
    # Show what was stored in memory
    memory = SystemMemory()
    goals = memory.get_active_goals()
    if goals:
        goal = goals[0]
        print(f"\nStored Goal: {goal.name}")
        print(f"Description: {goal.description}")
        
        tasks = memory.get_active_subtasks(goal.id)
        print(f"\nCreated {len(tasks)} tasks:")
        for task in tasks:
            print(f"  - {task.name} ({task.trigger_frequency} at {task.trigger_time})")


def demo_status():
    """Demonstrate status checking."""
    print("\n" + "="*60)
    print("DEMO: AGENT STATUS")
    print("="*60)
    
    agent = create_agent()
    status = agent.get_status()
    
    print(f"Active Goals: {status['active_goals']}")
    print(f"Active Tasks: {status['active_tasks']}")
    print(f"Recent Executions: {status['recent_executions']}")
    print(f"Success Rate: {status['success_rate']:.1f}%")
    
    if status['goals']:
        print("\nGoals:")
        for goal in status['goals']:
            print(f"  - {goal['name']} (ID: {goal['id']})")
    
    if status['tasks']:
        print("\nTasks:")
        for task in status['tasks']:
            print(f"  - {task['name']} ({task['frequency']}) (ID: {task['id']})")


def demo_act_react_phases():
    """Demonstrate Act and React phases."""
    print("\n" + "="*60)
    print("DEMO: ACT & REACT PHASES - Task Execution")
    print("="*60)
    
    # Get a task to execute
    memory = SystemMemory()
    tasks = memory.get_active_subtasks()
    
    if not tasks:
        print("No tasks available. Please run the planning demo first.")
        return
    
    task = tasks[0]
    print(f"\nExecuting task: {task.name}")
    
    cli_interface = MockCLIInterface()
    agent = create_agent(cli_interface)
    
    result = agent.trigger_scheduled_task(task.id)
    print(f"\nExecution Result: {result}")
    
    # Show execution logs
    logs = memory.get_execution_logs(days=1)
    if logs:
        latest_log = logs[0]
        print(f"\nExecution Log:")
        print(f"  Status: {latest_log.status.value}")
        print(f"  Triggered at: {latest_log.triggered_at}")
        if latest_log.completed_at:
            print(f"  Completed at: {latest_log.completed_at}")


def demo_reflect_phase():
    """Demonstrate Reflect phase."""
    print("\n" + "="*60)
    print("DEMO: REFLECT PHASE - Weekly Analysis")
    print("="*60)
    
    cli_interface = MockCLIInterface()
    agent = create_agent(cli_interface)
    
    print("\nAgent is running weekly reflection...")
    result = agent.run_reflection()
    print(f"\nReflection Result: {result}")


def main():
    """Run the complete demo."""
    print("Life Agent Demo - Plan-Act-React-Reflect Cycle")
    print("This demo shows how the Agent operates autonomously")
    print("treating the human as a tool to achieve goals.")
    
    try:
        # Demo each phase
        demo_plan_phase()
        demo_status()
        demo_act_react_phases()
        demo_reflect_phase()
        
        print("\n" + "="*60)
        print("DEMO COMPLETE")
        print("="*60)
        print("\nThe Life Agent has demonstrated:")
        print("✓ PLAN: Created a goal and broke it into tasks")
        print("✓ ACT: Triggered task execution")
        print("✓ REACT: Processed user responses")
        print("✓ REFLECT: Analyzed patterns and generated insights")
        print("\nTo use the real agent with interactive prompts:")
        print("  python3 main.py create-goal")
        print("  python3 main.py status")
        print("  python3 main.py help-commands")
        
    except Exception as e:
        print(f"\nDemo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
