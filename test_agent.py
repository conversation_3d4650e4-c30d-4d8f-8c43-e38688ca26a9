#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Test script for the Life Agent."""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from life_agent.core.agent import create_agent
from life_agent.core.config import get_config

def test_agent():
    """Test basic agent functionality."""
    try:
        print("Testing Life Agent...")
        
        # Test configuration
        print("1. Testing configuration...")
        try:
            config = get_config()
            print("   Configuration loaded successfully")
        except ValueError as e:
            print(f"   Configuration error: {e}")
            print("   Please set OPENAI_API_KEY in your .env file")
            return
        
        # Test agent creation
        print("2. Testing agent creation...")
        agent = create_agent()
        print("   Agent created successfully")
        
        # Test status
        print("3. Testing status...")
        status = agent.get_status()
        print(f"   Active goals: {status['active_goals']}")
        print(f"   Active tasks: {status['active_tasks']}")
        print(f"   Success rate: {status['success_rate']:.1f}%")
        
        print("\nAgent test completed successfully!")
        print("\nTo use the agent:")
        print("  python3 main.py create-goal    # Create a new goal")
        print("  python3 main.py status         # Check status")
        print("  python3 main.py help-commands  # See all commands")
        
    except Exception as e:
        print(f"Error testing agent: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_agent()
