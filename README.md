# Life Agent Demo

A revolutionary LangGraph-based AI agent that treats humans as tools to achieve long-term goals. Unlike traditional task management apps, this agent operates autonomously on a Plan-Act-React-Reflect cycle, continuously learning and adapting to help you succeed.

## Core Philosophy

**You are not using an app - the Agent is using YOU as a tool.**

The Life Agent is designed as a persistent, goal-driven AI that:
- Creates and manages your goals autonomously
- Schedules and triggers task execution
- Learns from your responses and failures
- Continuously optimizes your success patterns

## System Architecture

### Three Core Components

1. **Agent Core**: The intelligent brain that runs continuously, making decisions and learning
2. **Human Tool Interface**: The Agent's primary tool for interacting with you
3. **System Memory**: Persistent storage of all interactions, patterns, and learned insights

### The Agent's Main Loop: Plan-Act-React-Reflect

- **PLAN**: Agent guides you to create goals and break them into actionable tasks
- **ACT**: Agent triggers task execution at optimal times based on your patterns
- **REACT**: Agent processes your responses and updates execution status
- **REFLECT**: Agent analyzes patterns weekly and suggests improvements

## Features

- 🧠 **Autonomous Goal Management**: Agent-driven goal creation and task scheduling
- 📊 **Pattern Learning**: Continuous analysis of your success/failure patterns
- 🔄 **Adaptive Scheduling**: Time optimization based on your historical performance
- 💡 **Insight Generation**: Weekly reflection reports with actionable recommendations
- 🎯 **Failure Analysis**: Systematic understanding of why tasks fail
- 📈 **Success Optimization**: Data-driven improvements to your productivity

## Setup

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/life-agent/demo
   ```

2. **Install dependencies**:
   ```bash
   uv sync
   ```

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

4. **Install the package**:
   ```bash
   uv pip install -e .
   ```

## Usage

### Command Line Interface

**Create a new goal** (starts the PLAN phase):
```bash
python3 main.py create-goal
```

**Execute a scheduled task** (ACT phase):
```bash
python3 main.py execute-task <task_id>
```

**Run weekly reflection** (REFLECT phase):
```bash
python3 main.py reflect
```

**Check agent status**:
```bash
python3 main.py status
```

**Show configuration**:
```bash
python3 main.py config
```

**Get help**:
```bash
python3 main.py help-commands
```

### Python API

```python
from src.life_agent.core.agent import create_agent

# Create agent
agent = create_agent()

# Start goal planning
result = agent.create_goal()

# Execute a task
result = agent.trigger_scheduled_task(task_id=1)

# Run reflection
result = agent.run_reflection()

# Get status
status = agent.get_status()
print(f"Success rate: {status['success_rate']:.1f}%")
```

## How It Works

### 1. Planning Phase
- Agent asks you to define a goal
- Guides you to break it into actionable subtasks
- Collects scheduling preferences (frequency, time, duration)
- Analyzes historical patterns to suggest optimal timing
- Stores everything in System Memory

### 2. Action Phase
- Agent monitors scheduled tasks continuously
- Triggers execution reminders at optimal times
- Sends notifications/prompts when tasks are due
- Creates execution logs for tracking

### 3. React Phase
- Processes your responses (Started/Deferred/Ignored)
- Tracks completion status and feedback
- Records failure reasons for analysis
- Updates execution logs with outcomes

### 4. Reflect Phase
- Runs weekly analysis of execution patterns
- Identifies success/failure trends
- Generates insights and recommendations
- Suggests schedule adjustments based on data
- Stores insights for future reference

## Configuration

The agent can be configured through environment variables in your `.env` file:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
DEFAULT_MODEL=gpt-4o-mini
TEMPERATURE=0.7
MAX_TOKENS=1000

# LangSmith (Optional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=life-agent-demo
```

## Project Structure

```
src/life_agent/
├── core/
│   ├── agent.py      # Main Agent with Plan-Act-React-Reflect cycle
│   ├── config.py     # Configuration management
│   ├── state.py      # Agent state for the workflow
│   └── memory.py     # System Memory (SQLite database)
├── tools/
│   ├── human_tool.py # Human Tool Interface
│   └── basic_tools.py # Basic utility tools
└── cli/
    └── main.py       # CLI interface
```

## Database Schema

The System Memory uses SQLite with these tables:

- **goals**: Goal definitions and status
- **subtasks**: Task breakdown with scheduling info
- **execution_logs**: Complete history of task executions
- **insights**: Generated insights and recommendations

## Development

### Adding New Human Tool Functions

1. Add methods to `HumanToolInterface` in `human_tool.py`
2. The Agent can call these methods to interact with users
3. Implement CLI, web, or mobile interfaces as needed

### Extending Analysis Capabilities

1. Modify `_analyze_execution_patterns()` in `agent.py`
2. Add new insight types to the reflection phase
3. Enhance pattern recognition algorithms

### Adding Scheduling Logic

1. Implement `_get_due_tasks()` for real-time scheduling
2. Add time-based triggers and notifications
3. Integrate with system schedulers or cron jobs

## Requirements

- Python 3.12+
- OpenAI API key
- Dependencies managed by uv